import { logger } from '../utils/logger.js';
import Product from '../models/Product.js';
import Store from '../models/Store.js';
import StockItem from '../models/StockItem.js';
import { EmbedBuilder, ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, StringSelectMenuOptionBuilder } from 'discord.js';
import { BOT_CONFIG } from '../config/constants.js';

/**
 * Manipula interações de menus de seleção
 * @param {StringSelectMenuInteraction} interaction 
 */
export async function handleSelectMenu(interaction) {
    const customId = interaction.customId;
    const selectedValues = interaction.values;
    
    try {
        logger.info(`Menu selecionado: ${customId} por ${interaction.user.tag}, valores: ${selectedValues.join(', ')}`);

        // Roteamento baseado no customId do menu
        if (customId === 'store_product_select') {
            await handleStoreProductSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'edit_store_select') {
            await handleEditStoreSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'delete_store_select') {
            await handleDeleteStoreSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'create_product_store_select') {
            await handleCreateProductStoreSelect(interaction, selectedValues);
            return;
        }

        // Handlers para comandos de estoque
        if (customId === 'create_stock_store_select') {
            await handleCreateStockStoreSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'create_stock_product_select') {
            await handleCreateStockProductSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'edit_stock_store_select') {
            await handleEditStockStoreSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'edit_stock_product_select') {
            await handleEditStockProductSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'view_stock_store_select') {
            await handleViewStockStoreSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'delete_stock_store_select') {
            await handleDeleteStockStoreSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'delete_stock_product_select') {
            await handleDeleteStockProductSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'edit_product_store_select') {
            await handleEditProductStoreSelect(interaction, selectedValues);
            return;
        }

        if (customId === 'edit_product_product_select') {
            await handleEditProductProductSelect(interaction, selectedValues);
            return;
        }

        const [category, action] = customId.split('_');

        switch (category) {
            case 'store':
                await handleStoreSelectMenu(interaction, action, selectedValues);
                break;
            case 'admin':
                await handleAdminSelectMenu(interaction, action, selectedValues);
                break;
            default:
                logger.warn(`Categoria de menu não reconhecida: ${category}`);
                await interaction.reply({
                    content: '❌ Menu não reconhecido.',
                    ephemeral: true
                });
        }

    } catch (error) {
        logger.error(`Erro ao processar menu ${customId}:`, error);
        
        const errorMessage = {
            content: '❌ Erro ao processar a seleção do menu.',
            ephemeral: true
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp(errorMessage);
        } else {
            await interaction.reply(errorMessage);
        }
    }
}

/**
 * Manipula seleção de produtos na loja
 */
async function handleStoreProductSelect(interaction, selectedValues) {
    try {
        const selectedValue = selectedValues[0];

        // Casos especiais
        if (selectedValue === 'disabled') {
            return await interaction.reply({
                content: '📭 Não há produtos disponíveis no momento.',
                ephemeral: true
            });
        }

        if (selectedValue === 'cancel') {
            return await interaction.reply({
                content: '❌ Seleção cancelada.',
                ephemeral: true
            });
        }

        if (selectedValue === 'error') {
            return await interaction.reply({
                content: '❌ Erro ao carregar produtos. Tente novamente mais tarde.',
                ephemeral: true
            });
        }

        // Busca o produto selecionado
        const product = await Product.findById(selectedValue);

        if (!product) {
            return await interaction.reply({
                content: '❌ Produto não encontrado.',
                ephemeral: true
            });
        }

        if (product.status !== 'active') {
            return await interaction.reply({
                content: '❌ Este produto não está mais disponível.',
                ephemeral: true
            });
        }

        if (product.stock <= 0) {
            return await interaction.reply({
                content: '❌ Este produto está fora de estoque.',
                ephemeral: true
            });
        }

        // Cria embed com detalhes do produto
        const embed = new EmbedBuilder()
            .setTitle(`📦 ${product.name}`)
            .setDescription(product.description)
            .setColor(0x0099ff)
            .addFields(
                {
                    name: '💰 Preço',
                    value: `${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${product.price.toFixed(2)}`,
                    inline: true
                },
                {
                    name: '📊 Estoque',
                    value: product.stock.toString(),
                    inline: true
                },
                {
                    name: '🏷️ Categoria',
                    value: product.category || 'Sem categoria',
                    inline: true
                },
                {
                    name: '📋 Tipo',
                    value: product.isDigital ? '💾 Digital' : '📦 Físico',
                    inline: true
                }
            )
            .setTimestamp()
            .setFooter({
                text: `ID: ${product._id} | Vendidos: ${product.totalSold}`
            });

        // Adiciona imagem se disponível
        if (product.images && product.images.length > 0) {
            embed.setImage(product.images[0]);
        }

        await interaction.reply({
            embeds: [embed],
            ephemeral: true
        });

        // Incrementa visualizações
        await Product.findByIdAndUpdate(selectedValue, {
            $inc: { views: 1 }
        });

        logger.info(`Produto ${product.name} visualizado por ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de produto:', error);

        await interaction.reply({
            content: '❌ Erro ao carregar informações do produto.',
            ephemeral: true
        });
    }
}

/**
 * Manipula menus relacionados à loja
 */
async function handleStoreSelectMenu(interaction, action, selectedValues) {
    switch (action) {
        case 'category':
            await interaction.reply({
                content: `🏷️ Categoria selecionada: ${selectedValues.join(', ')}`,
                ephemeral: true
            });
            break;
        case 'product':
            await interaction.reply({
                content: `📦 Produto selecionado: ${selectedValues.join(', ')}`,
                ephemeral: true
            });
            break;
        default:
            await interaction.reply({
                content: '❌ Ação de menu de loja não reconhecida.',
                ephemeral: true
            });
    }
}

/**
 * Manipula menus administrativos
 */
async function handleAdminSelectMenu(interaction, action, selectedValues) {
    // Verificação básica de permissões
    if (!interaction.member.permissions.has('ADMINISTRATOR')) {
        return await interaction.reply({
            content: '❌ Você não tem permissão para usar este menu.',
            ephemeral: true
        });
    }

    switch (action) {
        case 'manage':
            await interaction.reply({
                content: `⚙️ Gerenciando: ${selectedValues.join(', ')}`,
                ephemeral: true
            });
            break;
        default:
            await interaction.reply({
                content: '❌ Ação de menu administrativo não reconhecida.',
                ephemeral: true
            });
    }
}

/**
 * Manipula seleção de loja para edição
 */
async function handleEditStoreSelect(interaction, selectedValues) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem editar lojas.',
                ephemeral: true
            });
        }

        const storeId = selectedValues[0];

        // Verifica se o usuário cancelou a seleção
        if (storeId === 'cancel') {
            return await interaction.update({
                content: '❌ Seleção cancelada.',
                components: [],
                ephemeral: true
            });
        }

        // Busca a loja selecionada
        const store = await Store.findById(storeId);

        if (!store || !store.isActive) {
            return await interaction.reply({
                content: '❌ Loja não encontrada ou inativa.',
                ephemeral: true
            });
        }

        // Verifica se a loja pertence ao servidor atual
        if (store.guildId !== interaction.guild.id) {
            return await interaction.reply({
                content: '❌ Esta loja não pertence a este servidor.',
                ephemeral: true
            });
        }

        // Cria o modal de edição com dados pré-preenchidos
        const modal = new ModalBuilder()
            .setCustomId(`store_edit_${storeId}`)
            .setTitle(`Editar Loja: ${store.name}`);

        // Campo para banner da loja (pré-preenchido)
        const bannerInput = new TextInputBuilder()
            .setCustomId('store_banner')
            .setLabel('Banner da Loja (URL da imagem)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Deixe em branco para manter o atual')
            .setRequired(false)
            .setMaxLength(500)
            .setValue(store.banner);

        // Campo para nome da loja (pré-preenchido)
        const nameInput = new TextInputBuilder()
            .setCustomId('store_name')
            .setLabel('Nome da Loja')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Deixe em branco para manter o atual')
            .setRequired(false)
            .setMaxLength(100)
            .setValue(store.name);

        // Campo para cor da loja (pré-preenchido)
        const colorInput = new TextInputBuilder()
            .setCustomId('store_color')
            .setLabel('Cor da Loja (hex ou nome)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Deixe em branco para manter a atual')
            .setRequired(false)
            .setMaxLength(50)
            .setValue(store.color);

        // Campo para descrição da loja (pré-preenchido)
        const descriptionInput = new TextInputBuilder()
            .setCustomId('store_description')
            .setLabel('Descrição da Loja')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Deixe em branco para manter a atual')
            .setRequired(false)
            .setMaxLength(1000)
            .setValue(store.description);

        // Criação das action rows
        const bannerRow = new ActionRowBuilder().addComponents(bannerInput);
        const nameRow = new ActionRowBuilder().addComponents(nameInput);
        const colorRow = new ActionRowBuilder().addComponents(colorInput);
        const descriptionRow = new ActionRowBuilder().addComponents(descriptionInput);

        // Adiciona os componentes ao modal
        modal.addComponents(bannerRow, nameRow, colorRow, descriptionRow);

        // Exibe o modal
        await interaction.showModal(modal);

        logger.info(`Modal de edição de loja "${store.name}" exibido para ${interaction.user.tag} em ${interaction.guild.name}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de loja para edição:', error);

        await interaction.reply({
            content: '❌ Erro ao carregar dados da loja para edição.',
            ephemeral: true
        });
    }
}

/**
 * Manipula seleção de loja para deleção
 */
async function handleDeleteStoreSelect(interaction, selectedValues) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem deletar lojas.',
                ephemeral: true
            });
        }

        const storeId = selectedValues[0];

        // Verifica se o usuário cancelou a seleção
        if (storeId === 'cancel') {
            return await interaction.update({
                content: '❌ Seleção cancelada.',
                components: [],
                ephemeral: true
            });
        }

        // Busca a loja selecionada
        const store = await Store.findById(storeId);

        if (!store || !store.isActive) {
            return await interaction.reply({
                content: '❌ Loja não encontrada ou inativa.',
                ephemeral: true
            });
        }

        // Verifica se a loja pertence ao servidor atual
        if (store.guildId !== interaction.guild.id) {
            return await interaction.reply({
                content: '❌ Esta loja não pertence a este servidor.',
                ephemeral: true
            });
        }

        // Busca informações do canal
        const channel = interaction.guild.channels.cache.get(store.channelId);
        const channelInfo = channel ? `#${channel.name}` : `ID: ${store.channelId} (canal não encontrado)`;

        // Cria botões de confirmação
        const confirmButton = new ButtonBuilder()
            .setCustomId(`delete_store_confirm_${storeId}`)
            .setLabel('Confirmar Exclusão')
            .setStyle(ButtonStyle.Danger)
            .setEmoji('🗑️');

        const cancelButton = new ButtonBuilder()
            .setCustomId('delete_store_cancel')
            .setLabel('Cancelar')
            .setStyle(ButtonStyle.Secondary)
            .setEmoji('❌');

        const row = new ActionRowBuilder().addComponents(confirmButton, cancelButton);

        await interaction.reply({
            content: `⚠️ **CONFIRMAÇÃO DE EXCLUSÃO**\n\n` +
                    `Você está prestes a deletar a loja:\n` +
                    `**Nome:** ${store.name}\n` +
                    `**Canal:** ${channelInfo}\n` +
                    `**Criada em:** ${store.createdAt.toLocaleDateString('pt-BR')}\n\n` +
                    `🚨 **Esta ação é IRREVERSÍVEL e irá:**\n` +
                    `• Deletar o canal da loja permanentemente\n` +
                    `• Remover todos os dados da loja do banco de dados\n` +
                    `• Apagar todas as mensagens relacionadas\n\n` +
                    `Tem certeza que deseja continuar?`,
            components: [row],
            ephemeral: true
        });

        logger.info(`Confirmação de deleção da loja "${store.name}" exibida para ${interaction.user.tag} em ${interaction.guild.name}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de loja para deleção:', error);

        await interaction.reply({
            content: '❌ Erro ao carregar dados da loja para deleção.',
            ephemeral: true
        });
    }
}

/**
 * Manipula seleção de loja para criação de produto
 */
async function handleCreateProductStoreSelect(interaction, selectedValues) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem criar produtos.',
                ephemeral: true
            });
        }

        const storeId = selectedValues[0];

        // Verifica se o usuário cancelou a seleção
        if (storeId === 'cancel') {
            return await interaction.update({
                content: '❌ Criação de produto cancelada.',
                components: [],
                ephemeral: true
            });
        }

        // Busca a loja selecionada
        const store = await Store.findById(storeId);

        if (!store || !store.isActive) {
            return await interaction.reply({
                content: '❌ Loja não encontrada ou inativa.',
                ephemeral: true
            });
        }

        // Verifica se a loja pertence ao servidor atual
        if (store.guildId !== interaction.guild.id) {
            return await interaction.reply({
                content: '❌ Esta loja não pertence a este servidor.',
                ephemeral: true
            });
        }

        // Cria o modal de criação de produto
        const modal = new ModalBuilder()
            .setCustomId(`product_create_${storeId}`)
            .setTitle(`Criar Produto - ${store.name}`);

        // Campo para nome do produto
        const nameInput = new TextInputBuilder()
            .setCustomId('product_name')
            .setLabel('Nome do Produto')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Digite o nome do produto')
            .setRequired(true)
            .setMinLength(3)
            .setMaxLength(100);

        // Campo para valor do produto
        const priceInput = new TextInputBuilder()
            .setCustomId('product_price')
            .setLabel('Valor do Produto (R$)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Ex: 29.99 ou 50')
            .setRequired(true)
            .setMaxLength(20);

        // Campo para emoji do produto (opcional)
        const emojiInput = new TextInputBuilder()
            .setCustomId('product_emoji')
            .setLabel('Emoji do Produto (opcional)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Ex: 🎮 ou <:custom:123456789>')
            .setRequired(false)
            .setMaxLength(50);

        // Criação das action rows
        const nameRow = new ActionRowBuilder().addComponents(nameInput);
        const priceRow = new ActionRowBuilder().addComponents(priceInput);
        const emojiRow = new ActionRowBuilder().addComponents(emojiInput);

        // Adiciona os componentes ao modal
        modal.addComponents(nameRow, priceRow, emojiRow);

        // Exibe o modal
        await interaction.showModal(modal);

        logger.info(`Modal de criação de produto exibido para ${interaction.user.tag} na loja "${store.name}"`);

    } catch (error) {
        logger.error('Erro ao processar seleção de loja para criação de produto:', error);

        await interaction.reply({
            content: '❌ Erro ao carregar dados da loja para criação do produto.',
            ephemeral: true
        });
    }
}

/**
 * Manipula seleção de loja para criar estoque
 */
async function handleCreateStockStoreSelect(interaction, selectedValues) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem gerenciar estoque.',
                ephemeral: true
            });
        }

        const storeId = selectedValues[0];

        // Verifica se o usuário cancelou a seleção
        if (storeId === 'cancel') {
            return await interaction.update({
                content: '❌ Criação de estoque cancelada.',
                components: [],
                ephemeral: true
            });
        }

        // Busca a loja selecionada
        const store = await Store.findById(storeId);

        if (!store || !store.isActive) {
            return await interaction.reply({
                content: '❌ Loja não encontrada ou inativa.',
                ephemeral: true
            });
        }

        // Verifica se a loja pertence ao servidor atual
        if (store.guildId !== interaction.guild.id) {
            return await interaction.reply({
                content: '❌ Esta loja não pertence a este servidor.',
                ephemeral: true
            });
        }

        // Busca produtos da loja
        const products = await Product.find({
            storeId: storeId,
            status: { $ne: 'discontinued' }
        }).sort({ name: 1 });

        if (products.length === 0) {
            return await interaction.update({
                content: '❌ Esta loja não possui produtos. Crie um produto primeiro usando `/criar-produto`.',
                components: [],
                ephemeral: true
            });
        }

        // Cria o select menu com os produtos disponíveis
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('create_stock_product_select')
            .setPlaceholder('Selecione o produto para adicionar estoque...')
            .setMinValues(1)
            .setMaxValues(1);

        // Adiciona os produtos como opções
        for (const product of products) {
            const stockCount = await StockItem.countByProduct(product._id);
            const statusEmoji = product.status === 'active' ? '✅' :
                               product.status === 'out_of_stock' ? '❌' : '⚠️';

            selectMenu.addOptions(
                new StringSelectMenuOptionBuilder()
                    .setLabel(product.name)
                    .setDescription(`${statusEmoji} Estoque atual: ${stockCount} • Preço: R$ ${product.price.toFixed(2)}`)
                    .setValue(`${storeId}|${product._id}`)
                    .setEmoji(product.emoji || '📦')
            );
        }

        // Adiciona opção de cancelar
        selectMenu.addOptions(
            new StringSelectMenuOptionBuilder()
                .setLabel('❌ Cancelar')
                .setDescription('Cancelar a criação de estoque')
                .setValue('cancel')
                .setEmoji('❌')
        );

        const row = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.update({
            content: `📦 **Adicionar Estoque - ${store.name}**\n\nSelecione o produto para adicionar estoque:`,
            components: [row],
            ephemeral: true
        });

        logger.info(`Produtos da loja "${store.name}" exibidos para criação de estoque por ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de loja para criar estoque:', error);

        await interaction.reply({
            content: '❌ Erro ao carregar produtos da loja.',
            ephemeral: true
        });
    }
}

/**
 * Manipula seleção de produto para criar estoque
 */
async function handleCreateStockProductSelect(interaction, selectedValues) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem gerenciar estoque.',
                ephemeral: true
            });
        }

        const selectedValue = selectedValues[0];

        // Verifica se o usuário cancelou a seleção
        if (selectedValue === 'cancel') {
            return await interaction.update({
                content: '❌ Criação de estoque cancelada.',
                components: [],
                ephemeral: true
            });
        }

        // Extrai storeId e productId do valor selecionado
        const [storeId, productId] = selectedValue.split('|');

        // Busca o produto selecionado
        const product = await Product.findById(productId);
        const store = await Store.findById(storeId);

        if (!product || !store) {
            return await interaction.reply({
                content: '❌ Produto ou loja não encontrados.',
                ephemeral: true
            });
        }

        // Cria o modal para adicionar estoque
        const modal = new ModalBuilder()
            .setCustomId(`stock_create_${storeId}_${productId}`)
            .setTitle(`Adicionar Estoque - ${product.name}`);

        // Campo para linhas de estoque
        const stockInput = new TextInputBuilder()
            .setCustomId('stock_lines')
            .setLabel('Linhas de Estoque')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Digite cada linha de estoque separada por quebra de linha:\<EMAIL>:senha123\<EMAIL>:senha456\<EMAIL>:senha789')
            .setRequired(true)
            .setMaxLength(2000);

        // Campo para observações (opcional)
        const notesInput = new TextInputBuilder()
            .setCustomId('stock_notes')
            .setLabel('Observações (opcional)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Observações administrativas sobre este estoque')
            .setRequired(false)
            .setMaxLength(500);

        // Criação das action rows
        const stockRow = new ActionRowBuilder().addComponents(stockInput);
        const notesRow = new ActionRowBuilder().addComponents(notesInput);

        // Adiciona os componentes ao modal
        modal.addComponents(stockRow, notesRow);

        // Exibe o modal
        await interaction.showModal(modal);

        logger.info(`Modal de criação de estoque exibido para produto "${product.name}" por ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de produto para criar estoque:', error);

        await interaction.reply({
            content: '❌ Erro ao carregar dados do produto.',
            ephemeral: true
        });
    }
}

/**
 * Manipula seleção de loja para editar estoque
 */
async function handleEditStockStoreSelect(interaction, selectedValues) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem gerenciar estoque.',
                ephemeral: true
            });
        }

        const storeId = selectedValues[0];

        // Verifica se o usuário cancelou a seleção
        if (storeId === 'cancel') {
            return await interaction.update({
                content: '❌ Edição de estoque cancelada.',
                components: [],
                ephemeral: true
            });
        }

        // Busca a loja selecionada
        const store = await Store.findById(storeId);

        if (!store || !store.isActive) {
            return await interaction.reply({
                content: '❌ Loja não encontrada ou inativa.',
                ephemeral: true
            });
        }

        // Verifica se a loja pertence ao servidor atual
        if (store.guildId !== interaction.guild.id) {
            return await interaction.reply({
                content: '❌ Esta loja não pertence a este servidor.',
                ephemeral: true
            });
        }

        // Busca produtos da loja que têm estoque
        const products = await Product.find({
            storeId: storeId,
            status: { $ne: 'discontinued' }
        }).sort({ name: 1 });

        if (products.length === 0) {
            return await interaction.update({
                content: '❌ Esta loja não possui produtos.',
                components: [],
                ephemeral: true
            });
        }

        // Filtra produtos que têm estoque disponível
        const productsWithStock = [];
        for (const product of products) {
            const stockCount = await StockItem.countByProduct(product._id, 'available');
            if (stockCount > 0) {
                productsWithStock.push({ product, stockCount });
            }
        }

        if (productsWithStock.length === 0) {
            return await interaction.update({
                content: '❌ Nenhum produto desta loja possui estoque disponível para edição.',
                components: [],
                ephemeral: true
            });
        }

        // Cria o select menu com os produtos que têm estoque
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('edit_stock_product_select')
            .setPlaceholder('Selecione o produto para editar estoque...')
            .setMinValues(1)
            .setMaxValues(1);

        // Adiciona os produtos como opções
        for (const { product, stockCount } of productsWithStock) {
            selectMenu.addOptions(
                new StringSelectMenuOptionBuilder()
                    .setLabel(product.name)
                    .setDescription(`Estoque disponível: ${stockCount} • Preço: R$ ${product.price.toFixed(2)}`)
                    .setValue(`${storeId}|${product._id}`)
                    .setEmoji(product.emoji || '📦')
            );
        }

        // Adiciona opção de cancelar
        selectMenu.addOptions(
            new StringSelectMenuOptionBuilder()
                .setLabel('❌ Cancelar')
                .setDescription('Cancelar a edição de estoque')
                .setValue('cancel')
                .setEmoji('❌')
        );

        const row = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.update({
            content: `✏️ **Editar Estoque - ${store.name}**\n\nSelecione o produto para editar estoque:`,
            components: [row],
            ephemeral: true
        });

        logger.info(`Produtos com estoque da loja "${store.name}" exibidos para edição por ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de loja para editar estoque:', error);

        await interaction.reply({
            content: '❌ Erro ao carregar produtos da loja.',
            ephemeral: true
        });
    }
}

/**
 * Manipula seleção de produto para editar estoque
 */
async function handleEditStockProductSelect(interaction, selectedValues) {
    try {
        // Verificação de permissões
        if (!interaction.member.permissions.has('Administrator')) {
            return await interaction.reply({
                content: '❌ Apenas administradores podem gerenciar estoque.',
                ephemeral: true
            });
        }

        const selectedValue = selectedValues[0];

        // Verifica se o usuário cancelou a seleção
        if (selectedValue === 'cancel') {
            return await interaction.update({
                content: '❌ Edição de estoque cancelada.',
                components: [],
                ephemeral: true
            });
        }

        // Extrai storeId e productId do valor selecionado
        const [storeId, productId] = selectedValue.split('|');

        // Busca o produto selecionado
        const product = await Product.findById(productId);
        const store = await Store.findById(storeId);

        if (!product || !store) {
            return await interaction.reply({
                content: '❌ Produto ou loja não encontrados.',
                ephemeral: true
            });
        }

        // Busca itens de estoque disponíveis do produto
        const stockItems = await StockItem.find({
            productId: productId,
            status: 'available'
        }).sort({ createdAt: 1 }).limit(25); // Limita a 25 itens para não sobrecarregar o menu

        if (stockItems.length === 0) {
            return await interaction.update({
                content: '❌ Este produto não possui estoque disponível para edição.',
                components: [],
                ephemeral: true
            });
        }

        // Cria o select menu com os itens de estoque
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId(`edit_stock_item_select_${storeId}_${productId}`)
            .setPlaceholder('Selecione o item de estoque para editar...')
            .setMinValues(1)
            .setMaxValues(1);

        // Adiciona os itens de estoque como opções
        for (let i = 0; i < stockItems.length; i++) {
            const item = stockItems[i];
            const preview = item.content.length > 50 ?
                item.content.substring(0, 47) + '...' :
                item.content;

            selectMenu.addOptions(
                new StringSelectMenuOptionBuilder()
                    .setLabel(`Item ${i + 1}`)
                    .setDescription(preview)
                    .setValue(item._id.toString())
                    .setEmoji('📝')
            );
        }

        // Adiciona opção de cancelar
        selectMenu.addOptions(
            new StringSelectMenuOptionBuilder()
                .setLabel('❌ Cancelar')
                .setDescription('Cancelar a edição')
                .setValue('cancel')
                .setEmoji('❌')
        );

        const row = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.update({
            content: `✏️ **Editar Estoque - ${product.name}**\n\nSelecione o item de estoque para editar:\n\n` +
                    `📊 **Estoque disponível:** ${stockItems.length} itens\n` +
                    `💰 **Preço:** R$ ${product.price.toFixed(2)}`,
            components: [row],
            ephemeral: true
        });

        logger.info(`Itens de estoque do produto "${product.name}" exibidos para edição por ${interaction.user.tag}`);

    } catch (error) {
        logger.error('Erro ao processar seleção de produto para editar estoque:', error);

        await interaction.reply({
            content: '❌ Erro ao carregar itens de estoque.',
            ephemeral: true
        });
    }
}

// Implementações básicas para os handlers restantes
async function handleViewStockStoreSelect(interaction, selectedValues) {
    // Implementação similar ao handleEditStockStoreSelect mas para visualização
    await interaction.reply({
        content: '🚧 Funcionalidade de visualização de estoque em desenvolvimento.',
        ephemeral: true
    });
}

async function handleDeleteStockStoreSelect(interaction, selectedValues) {
    // Implementação similar ao handleEditStockStoreSelect mas para deleção
    await interaction.reply({
        content: '🚧 Funcionalidade de deleção de estoque em desenvolvimento.',
        ephemeral: true
    });
}

async function handleDeleteStockProductSelect(interaction, selectedValues) {
    // Implementação para seleção de produto para deletar estoque
    await interaction.reply({
        content: '🚧 Funcionalidade de deleção de estoque em desenvolvimento.',
        ephemeral: true
    });
}

async function handleEditProductStoreSelect(interaction, selectedValues) {
    // Implementação para seleção de loja para editar produto
    await interaction.reply({
        content: '🚧 Funcionalidade de edição de produto em desenvolvimento.',
        ephemeral: true
    });
}

async function handleEditProductProductSelect(interaction, selectedValues) {
    // Implementação para seleção de produto para editar
    await interaction.reply({
        content: '🚧 Funcionalidade de edição de produto em desenvolvimento.',
        ephemeral: true
    });
}
